sudo: false
language: go

go:
  - "stable"
  - "1.11.x"
  - "1.10.x"
  - "1.9.x"

matrix:
  include:
    - go: "stable"
      env: GOLINT=true
  allow_failures:
    - go: tip
  fast_finish: true


before_install:
  - if [ ! -z "${GOLINT}" ]; then go get -u golang.org/x/lint/golint; fi

script:
  - go test --race ./...

after_script:
  - test -z "$(gofmt -s -l -w . | tee /dev/stderr)"
  - if [ ! -z  "${GOLINT}" ]; then echo running golint; golint --set_exit_status  ./...; else echo skipping golint; fi
  - go vet ./...

os:
  - linux
  - osx
  - windows

notifications:
  email: false

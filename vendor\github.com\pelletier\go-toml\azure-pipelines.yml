trigger:
- master

stages:
- stage: run_checks
  displayName: "Check"
  dependsOn: []
  jobs:
  - job: fmt
    displayName: "fmt"
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: GoTool@0
      displayName: "Install Go 1.16"
      inputs:
        version: "1.16"
    - task: Go@0
      displayName: "go fmt ./..."
      inputs:
        command: 'custom'
        customCommand: 'fmt'
        arguments: './...'
  - job: coverage
    displayName: "coverage"
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: GoTool@0
      displayName: "Install Go 1.16"
      inputs:
        version: "1.16"
    - task: Go@0
      displayName: "Generate coverage"
      inputs:
        command: 'test'
        arguments: "-race -coverprofile=coverage.txt -covermode=atomic"
    - task: Bash@3
      inputs:
        targetType: 'inline'
        script: 'bash <(curl -s https://codecov.io/bash) -t ${CODECOV_TOKEN}'
      env:
        CODECOV_TOKEN: $(CODECOV_TOKEN)
  - job: benchmark
    displayName: "benchmark"
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: GoTool@0
      displayName: "Install Go 1.16"
      inputs:
        version: "1.16"
    - script: echo "##vso[task.setvariable variable=PATH]${PATH}:/home/<USER>/go/bin/"
    - task: Bash@3
      inputs:
        filePath: './benchmark.sh'
        arguments: "master $(Build.Repository.Uri)"

  - job: go_unit_tests
    displayName: "unit tests"
    strategy:
      matrix:
        linux 1.16:
          goVersion: '1.16'
          imageName: 'ubuntu-latest'
        mac 1.16:
          goVersion: '1.16'
          imageName: 'macOS-latest'
        windows 1.16:
          goVersion: '1.16'
          imageName: 'windows-latest'
        linux 1.15:
          goVersion: '1.15'
          imageName: 'ubuntu-latest'
        mac 1.15:
          goVersion: '1.15'
          imageName: 'macOS-latest'
        windows 1.15:
          goVersion: '1.15'
          imageName: 'windows-latest'
    pool:
      vmImage: $(imageName)
    steps:
    - task: GoTool@0
      displayName: "Install Go $(goVersion)"
      inputs:
        version: $(goVersion)
    - task: Go@0
      displayName: "go test ./..."
      inputs:
        command: 'test'
        arguments: './...'
- stage: build_binaries
  displayName: "Build binaries"
  dependsOn: run_checks
  jobs:
  - job: build_binary
    displayName: "Build binary"
    strategy:
      matrix:
        linux_amd64:
          GOOS: linux
          GOARCH: amd64
        darwin_amd64:
          GOOS: darwin
          GOARCH: amd64
        windows_amd64:
          GOOS: windows
          GOARCH: amd64
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: GoTool@0
      displayName: "Install Go"
      inputs:
        version: 1.16
    - task: Bash@3
      inputs:
        targetType: inline
        script: "make dist"
      env:
        go.goos: $(GOOS)
        go.goarch: $(GOARCH)
    - task: CopyFiles@2
      inputs:
        sourceFolder: '$(Build.SourcesDirectory)'
        contents: '*.tar.xz'
        TargetFolder: '$(Build.ArtifactStagingDirectory)'
    - task: PublishBuildArtifacts@1
      inputs:
        pathtoPublish: '$(Build.ArtifactStagingDirectory)'
        artifactName: binaries
- stage: build_binaries_manifest
  displayName: "Build binaries manifest"
  dependsOn: build_binaries
  jobs:
  - job: build_manifest
    displayName: "Build binaries manifest"
    steps:
      - task: DownloadBuildArtifacts@0
        inputs:
          buildType: 'current'
          downloadType: 'single'
          artifactName: 'binaries'
          downloadPath: '$(Build.SourcesDirectory)'
      - task: Bash@3
        inputs:
          targetType: inline
          script: "cd binaries && sha256sum --binary *.tar.xz | tee $(Build.ArtifactStagingDirectory)/sha256sums.txt"
      - task: PublishBuildArtifacts@1
        inputs:
          pathtoPublish: '$(Build.ArtifactStagingDirectory)'
          artifactName: manifest

- stage: build_docker_image
  displayName: "Build Docker image"
  dependsOn: run_checks
  jobs:
  - job: build
    displayName: "Build"
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: Docker@2
      inputs:
        command: 'build'
        Dockerfile: 'Dockerfile'
        buildContext: '.'
        addPipelineData: false

- stage: publish_docker_image
  displayName: "Publish Docker image"
  dependsOn: build_docker_image
  condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'master'))
  jobs:
  - job: publish
    displayName: "Publish"
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: Docker@2
      inputs:
        containerRegistry: 'DockerHub'
        repository: 'pelletier/go-toml'
        command: 'buildAndPush'
        Dockerfile: 'Dockerfile'
        buildContext: '.'
        tags: 'latest'

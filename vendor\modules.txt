# github.com/fsnotify/fsnotify v1.4.9
## explicit; go 1.13
github.com/fsnotify/fsnotify
# github.com/hashicorp/hcl v1.0.0
## explicit
github.com/hashicorp/hcl
github.com/hashicorp/hcl/hcl/ast
github.com/hashicorp/hcl/hcl/parser
github.com/hashicorp/hcl/hcl/printer
github.com/hashicorp/hcl/hcl/scanner
github.com/hashicorp/hcl/hcl/strconv
github.com/hashicorp/hcl/hcl/token
github.com/hashicorp/hcl/json/parser
github.com/hashicorp/hcl/json/scanner
github.com/hashicorp/hcl/json/token
# github.com/magiconair/properties v1.8.5
## explicit; go 1.13
github.com/magiconair/properties
# github.com/mitchellh/mapstructure v1.4.1
## explicit; go 1.14
github.com/mitchellh/mapstructure
# github.com/op/go-logging v0.0.0-20160315200505-970db520ece7
## explicit
github.com/op/go-logging
# github.com/pelletier/go-toml v1.9.3
## explicit; go 1.12
github.com/pelletier/go-toml
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/sirupsen/logrus v1.8.1
## explicit; go 1.13
github.com/sirupsen/logrus
# github.com/spf13/afero v1.6.0
## explicit; go 1.13
github.com/spf13/afero
github.com/spf13/afero/mem
# github.com/spf13/cast v1.3.1
## explicit
github.com/spf13/cast
# github.com/spf13/jwalterweatherman v1.1.0
## explicit
github.com/spf13/jwalterweatherman
# github.com/spf13/pflag v1.0.5
## explicit; go 1.12
github.com/spf13/pflag
# github.com/spf13/viper v1.8.1
## explicit; go 1.12
github.com/spf13/viper
# github.com/subosito/gotenv v1.2.0
## explicit
github.com/subosito/gotenv
# golang.org/x/sys v0.0.0-20210510120138-977fb7262007
## explicit; go 1.17
golang.org/x/sys/internal/unsafeheader
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/text v0.3.5
## explicit; go 1.11
golang.org/x/text/transform
golang.org/x/text/unicode/norm
# gopkg.in/ini.v1 v1.62.0
## explicit
gopkg.in/ini.v1
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
gopkg.in/yaml.v2

# Names should be added to this file as
#	Name or Organization <email address>
# The email address is not required for organizations.

# You can update this list using the following command:
#
#   $ git shortlog -se | awk '{print $2 " " $3 " " $4}'

# Please keep the list sorted.

<PERSON> <<EMAIL>>
<PERSON><PERSON> <<EMAIL>>
<PERSON><PERSON> <<EMAIL>>
<PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>> <<EMAIL>>
<PERSON><PERSON><PERSON> Buchholz <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> haran <<EMAIL>>
<PERSON>w
Kelvin Fo <<EMAIL>>
<PERSON><PERSON><PERSON><PERSON><PERSON> MATSUZAWA <<EMAIL>>
<PERSON> <mdl<PERSON>@gmail.com>
<PERSON> <***************>
<PERSON>olai <PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
Pawel Knap <<EMAIL>>
Pieter Droogendijk <<EMAIL>>
Pursuit92 <<EMAIL>>
Riku Voipio <<EMAIL>>
Rob Figueiredo <<EMAIL>>
Rodrigo Chiossi <<EMAIL>>
Slawek Ligus <<EMAIL>>
Soge Zhang <<EMAIL>>
Tiffany Jernigan <<EMAIL>>
Tilak Sharma <<EMAIL>>
Tom Payne <<EMAIL>>
Travis Cline <<EMAIL>>
Tudor Golubenco <<EMAIL>>
Vahe Khachikyan <<EMAIL>>
Yukang <<EMAIL>>
bronze1man <<EMAIL>>
debrando <<EMAIL>>
henrikedwards <<EMAIL>>
铁哥 <<EMAIL>>
